import { _decorator, Component, Node } from 'cc';
import { GameData, BirdType, GameMode } from './GameData';
import { ItemManager, ItemType } from './ItemManager';
const { ccclass, property } = _decorator;

/**
 * Bug修复测试类
 * 测试金币四舍五入和复活双倍金币卡补偿功能
 */
@ccclass('BugFixTest')
export class BugFixTest extends Component {

    start() {
        console.log("=== Bug修复测试开始 ===");
        this.runAllTests();
    }

    private runAllTests() {
        this.testCoinRounding();
        this.testReviveDoubleCoinCompensation();
    }

    /**
     * 测试金币四舍五入功能
     */
    private testCoinRounding() {
        console.log("\n--- 测试1: 金币四舍五入测试 ---");
        
        // 重置状态
        GameData.resetSessionCoins();
        GameData.clearLuckyDiceMultiplier();
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        ItemManager.deactivateItemEffect(ItemType.LUCKY_DICE);
        
        // 设置测试环境：金色小鸟 + 标准关卡 + 幸运骰子(1倍)
        GameData.setCurrentGameMode(GameMode.NORMAL_STANDARD);
        GameData.setSelectedBirdType(BirdType.GOLD);
        ItemManager.setItemCount(ItemType.LUCKY_DICE, 1);
        ItemManager.activateItemEffect(ItemType.LUCKY_DICE);
        GameData.setLuckyDiceMultiplier(1);
        
        // 测试案例1: 收集3个金币
        console.log("\n测试案例1: 收集3个金币");
        GameData.resetSessionCoins();
        GameData.addCoin(3);
        
        const sessionCoins1 = GameData.getSessionCoins();
        const finalCoins1 = GameData.getFinalSessionCoins();
        const multiplier1 = GameData.getCurrentCoinMultiplier();
        
        console.log(`原始金币: ${sessionCoins1}`);
        console.log(`倍率: ${multiplier1} (1.2 × 1 × 1.5 × 1)`);
        console.log(`计算结果: ${sessionCoins1 * multiplier1}`);
        console.log(`最终金币: ${finalCoins1}`);
        
        // 3 * 1.8 = 5.4，四舍五入应该是5
        if (sessionCoins1 === 3 && finalCoins1 === 5 && Math.abs(multiplier1 - 1.8) < 0.001) {
            console.log("✅ 案例1通过 (5.4 → 5)");
        } else {
            console.error(`❌ 案例1失败: 期望5, 实际${finalCoins1}`);
        }
        
        // 测试案例2: 收集7个金币
        console.log("\n测试案例2: 收集7个金币");
        GameData.resetSessionCoins();
        GameData.addCoin(7);
        
        const sessionCoins2 = GameData.getSessionCoins();
        const finalCoins2 = GameData.getFinalSessionCoins();
        
        console.log(`原始金币: ${sessionCoins2}`);
        console.log(`计算结果: ${sessionCoins2 * multiplier1}`);
        console.log(`最终金币: ${finalCoins2}`);
        
        // 7 * 1.8 = 12.6，四舍五入应该是13
        if (sessionCoins2 === 7 && finalCoins2 === 13) {
            console.log("✅ 案例2通过 (12.6 → 13)");
        } else {
            console.error(`❌ 案例2失败: 期望13, 实际${finalCoins2}`);
        }
        
        // 测试案例3: 收集5个金币
        console.log("\n测试案例3: 收集5个金币");
        GameData.resetSessionCoins();
        GameData.addCoin(5);
        
        const sessionCoins3 = GameData.getSessionCoins();
        const finalCoins3 = GameData.getFinalSessionCoins();
        
        console.log(`原始金币: ${sessionCoins3}`);
        console.log(`计算结果: ${sessionCoins3 * multiplier1}`);
        console.log(`最终金币: ${finalCoins3}`);
        
        // 5 * 1.8 = 9.0，应该是9
        if (sessionCoins3 === 5 && finalCoins3 === 9) {
            console.log("✅ 案例3通过 (9.0 → 9)");
        } else {
            console.error(`❌ 案例3失败: 期望9, 实际${finalCoins3}`);
        }
        
        console.log("✅ 金币四舍五入测试完成");
    }

    /**
     * 测试复活金币计算逻辑
     */
    private testReviveDoubleCoinCompensation() {
        console.log("\n--- 测试2: 复活金币计算逻辑测试 ---");

        // 重置状态
        GameData.resetSessionCoins();
        GameData.resetSessionReviveCount();
        GameData.clearReviveState();
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        ItemManager.deactivateItemEffect(ItemType.LUCKY_DICE);

        // 设置测试环境：挑战模式 + 金色小鸟 + 双倍金币卡 + 幸运骰子
        GameData.setCurrentGameMode(GameMode.CHALLENGE_WIND);
        GameData.setSelectedBirdType(BirdType.GOLD);
        ItemManager.setItemCount(ItemType.DOUBLE_COIN, 2);
        ItemManager.setItemCount(ItemType.REVIVE_COIN, 1);
        ItemManager.setItemCount(ItemType.LUCKY_DICE, 1);

        // 激活道具
        ItemManager.activateItemEffect(ItemType.DOUBLE_COIN);
        ItemManager.activateItemEffect(ItemType.LUCKY_DICE);
        GameData.setLuckyDiceMultiplier(3); // 设置幸运骰子为3倍

        console.log("\n=== 模拟完整游戏流程 ===");

        // 设置初始总金币
        const initialTotalCoins = 911;
        localStorage.setItem("TotalCoins", initialTotalCoins.toString());
        console.log(`初始总金币: ${initialTotalCoins}`);

        // 第一阶段：收集4枚金币后死亡
        console.log("\n1. 第一阶段：收集4枚金币");
        GameData.resetSessionCoins();

        // 模拟收集金币（不使用addCoin，因为它会直接累加到总金币）
        GameData.setSessionCoinsForTest(4);

        const sessionCoins1 = GameData.getSessionCoins();
        const finalCoins1 = GameData.getFinalSessionCoins();
        const multiplier = GameData.getCurrentCoinMultiplier();

        console.log(`本局原始金币: ${sessionCoins1}`);
        console.log(`倍率: ${multiplier} (2.0 × 3 × 1.5 × 2)`);
        console.log(`本局最终金币: ${finalCoins1}`);

        // 模拟第一次死亡时的金币累加
        const totalAfterFirstDeath = initialTotalCoins + sessionCoins1; // 原始累加
        localStorage.setItem("TotalCoins", totalAfterFirstDeath.toString());
        console.log(`第一次死亡后总金币（原始累加）: ${totalAfterFirstDeath}`);

        // 应用倍率效果
        GameData.applyItemEffectsToTotalCoins();
        const totalAfterMultiplier1 = GameData.getTotalCoins();
        console.log(`应用倍率后总金币: ${totalAfterMultiplier1}`);

        // 设置复活状态
        GameData.setReviveState(5, sessionCoins1);
        console.log(`复活状态已设置，保存的最终金币: ${GameData.getRevivedFinalCoins()}`);

        // 第二阶段：复活后再收集2枚金币
        console.log("\n2. 第二阶段：复活后收集2枚金币");

        // 复活补偿双倍金币卡
        if (ItemManager.isItemActive(ItemType.DOUBLE_COIN)) {
            const currentDoubleCoinCount = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
            ItemManager.setItemCount(ItemType.DOUBLE_COIN, currentDoubleCoinCount + 1);
            console.log(`复活补偿：双倍金币卡数量 +1`);
        }

        // 恢复游戏状态
        GameData.applyReviveState();

        // 模拟再收集2枚金币
        GameData.addCoin(2); // 这会累加到总金币

        const sessionCoins2 = GameData.getSessionCoins();
        const finalCoins2 = GameData.getFinalSessionCoins();

        console.log(`复活后本局原始金币: ${sessionCoins2}`);
        console.log(`复活后本局最终金币: ${finalCoins2}`);

        // 第二次死亡，应用倍率效果
        console.log("\n3. 第二次死亡，应用倍率效果");
        const totalBeforeSecondDeath = GameData.getTotalCoins();
        console.log(`第二次死亡前总金币: ${totalBeforeSecondDeath}`);

        GameData.applyItemEffectsToTotalCoins();
        const finalTotalCoins = GameData.getTotalCoins();
        console.log(`最终总金币: ${finalTotalCoins}`);

        // 验证结果
        console.log("\n=== 验证结果 ===");
        console.log("预期计算逻辑：");
        console.log(`- 初始总金币: ${initialTotalCoins}`);
        console.log(`- 游戏进行中累加原始金币: ${initialTotalCoins} + ${sessionCoins2} = ${initialTotalCoins + sessionCoins2}`);
        console.log(`- 复活撤销之前累加的原始金币: ${initialTotalCoins + sessionCoins2} - ${sessionCoins1} = ${initialTotalCoins + sessionCoins2 - sessionCoins1}`);
        console.log(`- 应用最终倍率差额: ${initialTotalCoins + sessionCoins2 - sessionCoins1} + ${finalCoins2 - sessionCoins2} = ${initialTotalCoins + sessionCoins2 - sessionCoins1 + (finalCoins2 - sessionCoins2)}`);
        console.log(`- 简化计算: ${initialTotalCoins} + ${finalCoins2} = ${initialTotalCoins + finalCoins2}`);

        const expectedTotal = initialTotalCoins + finalCoins2;

        if (Math.abs(finalTotalCoins - expectedTotal) < 1) {
            console.log("✅ 复活金币计算逻辑测试通过");
        } else {
            console.error(`❌ 复活金币计算逻辑测试失败: 期望${expectedTotal}, 实际${finalTotalCoins}`);
        }

        console.log("=== 复活金币计算测试结束 ===");
    }

    /**
     * 静态方法，可以从控制台手动调用
     */
    public static runManualTest() {
        console.log("=== 手动运行Bug修复测试 ===");
        const testInstance = new BugFixTest();
        testInstance.runAllTests();
    }
}
