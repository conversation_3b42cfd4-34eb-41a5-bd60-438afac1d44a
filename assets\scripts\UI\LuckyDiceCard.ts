import { _decorator, Component, Node, Button, Label } from 'cc';
import { ItemManager, ItemType } from '../ItemManager';
import { GameData } from '../GameData';
const { ccclass, property } = _decorator;

/**
 * 幸运骰子UI控制器
 * 负责处理幸运骰子的购买、使用、显示等UI逻辑
 */
@ccclass('LuckyDiceCard')
export class LuckyDiceCard extends Component {

    @property(Button)
    purchaseButton: Button = null;

    @property(Button)
    useButton: Button = null;

    @property(Button)
    disableButton: Button = null;

    @property(Label)
    priceLabel: Label = null;

    @property(Node)
    coinIcon: Node = null;

    @property(Node)
    coinLackSprite: Node = null; // 金币不足提示节点

    start() {
        this.initializeUI();
        this.setupButtonEvents();
        this.updateDisplay();
    }

    onEnable() {
        // 每次节点激活时刷新显示状态
        this.scheduleOnce(() => {
            this.updateDisplay();
        }, 0.1);
    }

    /**
     * 初始化UI
     */
    private initializeUI(): void {
        // 设置价格标签
        if (this.priceLabel) {
            const price = ItemManager.getItemPrice(ItemType.LUCKY_DICE);
            this.priceLabel.string = price.toString();
        }

        console.log("LuckyDiceCard UI 初始化完成");
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        // 购买按钮事件
        if (this.purchaseButton) {
            this.purchaseButton.node.on(Button.EventType.CLICK, this.onPurchaseClick, this);
        }

        // 使用按钮事件
        if (this.useButton) {
            this.useButton.node.on(Button.EventType.CLICK, this.onUseClick, this);
        }

        // 禁用按钮事件
        if (this.disableButton) {
            this.disableButton.node.on(Button.EventType.CLICK, this.onDisableClick, this);
        }
    }

    /**
     * 购买按钮点击事件
     */
    private onPurchaseClick(): void {
        console.log("点击购买幸运骰子");

        const success = ItemManager.purchaseItem(ItemType.LUCKY_DICE);
        if (success) {
            this.updateDisplay();
            console.log("购买成功！");
        } else {
            console.log("购买失败，金币不足");
            this.showCoinLackSprite();
        }
    }

    /**
     * 使用按钮点击事件
     */
    private onUseClick(): void {
        console.log("点击使用幸运骰子");

        const currentCount = ItemManager.getItemCount(ItemType.LUCKY_DICE);
        if (currentCount <= 0) {
            console.log("幸运骰子数量不足");
            this.showCoinLackSprite();
            return;
        }

        // 只激活效果，不减少数量（永久道具）
        ItemManager.activateItemEffect(ItemType.LUCKY_DICE);
        this.updateDisplay();
        console.log("幸运骰子已激活！");
    }

    /**
     * 禁用按钮点击事件
     */
    private onDisableClick(): void {
        console.log("点击禁用幸运骰子");

        ItemManager.deactivateItemEffect(ItemType.LUCKY_DICE);
        this.updateDisplay();
        console.log("幸运骰子已禁用");
    }

    /**
     * 更新显示
     */
    public updateDisplay(): void {
        const currentCount = ItemManager.getItemCount(ItemType.LUCKY_DICE);
        const isActive = ItemManager.isItemActive(ItemType.LUCKY_DICE);
        const isPurchased = currentCount > 0;

        // 购买按钮：未购买时显示
        if (this.purchaseButton) {
            this.purchaseButton.node.active = !isPurchased;
            
            if (!isPurchased) {
                // 检查是否有足够金币购买
                const totalCoins = GameData.getTotalCoins();
                const price = ItemManager.getItemPrice(ItemType.LUCKY_DICE);
                this.purchaseButton.interactable = totalCoins >= price;
            }
        }

        // 价格标签和金币图标：未购买时显示
        if (this.priceLabel) {
            this.priceLabel.node.active = !isPurchased;
        }
        if (this.coinIcon) {
            this.coinIcon.active = !isPurchased;
        }

        // 使用按钮：已购买且未激活时显示
        if (this.useButton) {
            this.useButton.node.active = isPurchased && !isActive;
        }

        // 禁用按钮：已购买且已激活时显示
        if (this.disableButton) {
            this.disableButton.node.active = isPurchased && isActive;
        }

        console.log(`LuckyDiceCard 显示更新: 数量=${currentCount}, 激活=${isActive}, 已购买=${isPurchased}`);
    }

    /**
     * 显示金币不足提示
     */
    private showCoinLackSprite(): void {
        if (this.coinLackSprite) {
            this.coinLackSprite.active = true;
            // 2秒后自动隐藏
            this.scheduleOnce(() => {
                if (this.coinLackSprite && this.coinLackSprite.isValid) {
                    this.coinLackSprite.active = false;
                }
            }, 2.0);
        }
    }

    /**
     * 外部调用更新显示（比如从其他UI返回时）
     */
    public refreshDisplay(): void {
        this.updateDisplay();
    }

    onDestroy() {
        // 移除事件监听
        if (this.purchaseButton && this.purchaseButton.node && this.purchaseButton.node.isValid) {
            this.purchaseButton.node.off(Button.EventType.CLICK, this.onPurchaseClick, this);
        }

        if (this.useButton && this.useButton.node && this.useButton.node.isValid) {
            this.useButton.node.off(Button.EventType.CLICK, this.onUseClick, this);
        }

        if (this.disableButton && this.disableButton.node && this.disableButton.node.isValid) {
            this.disableButton.node.off(Button.EventType.CLICK, this.onDisableClick, this);
        }
    }
}
