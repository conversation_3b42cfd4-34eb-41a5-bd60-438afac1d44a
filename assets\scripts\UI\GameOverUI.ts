import { _decorator, Component, director, Label, Node, Button } from 'cc';
import { GameData, GameMode } from '../GameData';
import { EnergyManager } from '../EnergyManager';
import { GameManager } from '../GameManager';
import { AudioMgr } from '../AudioMgr';
import { ItemManager, ItemType } from '../ItemManager';
const { ccclass, property } = _decorator;

@ccclass('GameOverUI')
export class GameOverUI extends Component {

    @property(Label)
    curScoreLabel:Label = null;
    @property(Label)
    bestScoreLabel:Label = null;
    @property(Label)
    sessionCoinsLabel:Label = null;
    @property(Label)
    magnificationLabel:Label = null;

    @property(Node)
    newSprite:Node = null;

    @property([Node])
    medalArray:Node[] = [];

    // 复活相关UI
    @property(Button)
    reviveButton: Button = null;

    @property(Node)
    reviveLackSprite: Node = null; // 复活币不足提示节点

    @property(Node)
    reviveLimitSprite: Node = null; // 复活次数达到上限提示节点

    start() {
        // 设置复活按钮事件
        if (this.reviveButton) {
            this.reviveButton.node.on(Button.EventType.CLICK, this.onReviveButtonClick, this);
        }

        // 确保复活币不足提示初始隐藏
        if (this.reviveLackSprite) {
            this.reviveLackSprite.active = false;
        }

        // 确保复活次数上限提示初始隐藏
        if (this.reviveLimitSprite) {
            this.reviveLimitSprite.active = false;
        }
    }

    onDestroy() {
        // 移除事件监听，防止内存泄漏
        if (this.reviveButton && this.reviveButton.node && this.reviveButton.node.isValid) {
            this.reviveButton.node.off(Button.EventType.CLICK, this.onReviveButtonClick, this);
        }
    }

    public show(curScore:number,bestScrore:number){
        this.node.active=true;

        // 获取当前游戏模式
        const currentMode = GameData.getCurrentGameMode();
        const modeName = GameData.getGameModeName(currentMode);

        // 获取当前模式的最高分
        const currentModeBestScore = GameData.getBestScore(currentMode);

        this.curScoreLabel.string = curScore.toString();
        this.bestScoreLabel.string = currentModeBestScore.toString();

        // 如果幸运骰子激活但还没有生成倍率，现在生成
        const isLuckyDiceActive = localStorage.getItem("Item_Active_3") === "true";
        const currentDiceMultiplier = GameData.getLuckyDiceMultiplier();
        if (isLuckyDiceActive && currentDiceMultiplier === 1 && !localStorage.getItem("LuckyDice_CurrentMultiplier")) {
            // 只有在激活且没有保存倍率时才生成新的随机倍率
            const diceMultiplier = ItemManager.generateLuckyDiceMultiplier();
            GameData.setLuckyDiceMultiplier(diceMultiplier);
            console.log(`生成幸运骰子倍率: ${diceMultiplier}`);
        }

        // 应用道具效果到总金币
        GameData.applyItemEffectsToTotalCoins();

        // 获取金币倍数和最终金币数
        const multiplierInfo = GameData.getMultiplierInfo();
        const originalSessionCoins = GameData.getSessionCoins();
        const finalSessionCoins = GameData.getFinalSessionCoins();

        // 显示本局收集的金币数（应用道具效果后）
        if (this.sessionCoinsLabel) {
            // SessionCoins标签始终显示
            this.sessionCoinsLabel.node.active = true;
            if (multiplierInfo.total > 1) {
                this.sessionCoinsLabel.string = finalSessionCoins.toString();
                console.log(`显示本局最终金币数: ${finalSessionCoins} (原始: ${originalSessionCoins}, 总倍数: ${multiplierInfo.total})`);
            } else {
                this.sessionCoinsLabel.string = originalSessionCoins.toString();
                console.log(`显示本局原始金币数: ${originalSessionCoins}`);
            }
        }

        // 显示倍数标签
        if (this.magnificationLabel) {
            if (multiplierInfo.total > 1) {
                this.magnificationLabel.node.active = true;

                // 构建倍率显示文本
                let multiplierText = "";
                if (multiplierInfo.luckyDice > 1 && multiplierInfo.doubleCoin > 1) {
                    // 两种道具都激活
                    multiplierText = `X${multiplierInfo.luckyDice}X${multiplierInfo.doubleCoin}=${multiplierInfo.total}!`;
                } else if (multiplierInfo.luckyDice > 1) {
                    // 只有幸运骰子激活
                    multiplierText = `X${multiplierInfo.luckyDice}!`;
                } else if (multiplierInfo.doubleCoin > 1) {
                    // 只有双倍金币卡激活
                    multiplierText = `X${multiplierInfo.doubleCoin}!`;
                }

                this.magnificationLabel.string = multiplierText;
                console.log(`显示金币倍数: ${multiplierText} (骰子:${multiplierInfo.luckyDice}, 双倍:${multiplierInfo.doubleCoin})`);
            } else {
                this.magnificationLabel.node.active = false;
                console.log("金币倍数为1，隐藏倍数标签");
            }
        }

        // 显示"NEW"标志（基于当前模式的最高分）
        if(curScore > currentModeBestScore){
            this.newSprite.active = true;
        }else{
            this.newSprite.active = false;
        }

        // 隐藏所有奖牌
        for (let i = 0; i < this.medalArray.length; i++) {
            this.medalArray[i].active = false;
        }

        // 先保存分数，确保历史记录已更新
        GameData.saveScore(currentMode);

        // 获取当前分数在历史前三高分中的排名
        const rank = GameData.getCurrentRank(currentMode);

        // 获取并输出最高三次分数（调试用）
        const topScores = GameData.getTopScores(currentMode);
        console.log(`=== ${modeName} 游戏结束 ===`);
        console.log("当前最高三次分数：", topScores);
        console.log("当前分数：", curScore);
        console.log("当前模式最高分：", currentModeBestScore);
        console.log("当前排名：", rank);
        console.log("奖牌索引：", (rank >= 0 && rank < 3) ? (3 - rank) : 0);

        // 根据排名显示对应的奖牌
        // rank: 0-金牌, 1-银牌, 2-铜牌, -1-木牌(未进入前三)
        if (rank >= 0 && rank < 3) {
            // 显示对应的奖牌（金、银、铜）
            this.medalArray[3 - rank].active = true; // 3-金, 2-银, 1-铜
            console.log("显示奖牌：", 3 - rank); // 调试信息
        } else {
            // 未进入前三，显示木牌
            this.medalArray[0].active = true;
            console.log("显示木牌"); // 调试信息
        }

        // 打印所有模式的记录（调试用）
        GameData.printAllGameRecords();

        // 消耗激活的道具并清理效果（游戏结束后）
        ItemManager.consumeActiveItems();
        console.log("已消耗激活道具并清理游戏效果");
    }

    public hide(){
        this.node.active=false;
    }

    onPlayButtonClick(){
        // 检查体力是否足够
        const energyManager = EnergyManager.getInstance();
        if (energyManager && energyManager.getCurrentEnergy() < EnergyManager.ENERGY_PER_GAME) {
            console.log("体力不足，无法重新开始游戏");
            // 获取GameManager实例
            const gameManager = GameManager.inst();
            if (gameManager && gameManager.energyExhaustedPanel) {
                // 显示体力不足提示面板
                gameManager.energyExhaustedPanel.show();
            }
            return;
        }

        // 重置分数，解决分数累计的问题
        GameData.resetScore();
        // 清除复活状态
        GameData.clearReviveState();
        // 重置本局复活次数
        GameData.resetSessionReviveCount();
        director.loadScene(director.getScene().name);
    }

    // 点击复活按钮
    onReviveButtonClick(){
        console.log("=== 点击复活按钮 ===");
        console.log(`当前复活次数: ${GameData.getSessionReviveCount()}, 是否可以复活: ${GameData.canRevive()}`);

        // 首先检查复活次数是否已达上限
        if (!GameData.canRevive()) {
            console.log("本局复活次数已达上限，显示提示");
            this.showReviveLimitSprite();
            return;
        }

        const reviveCoinCount = ItemManager.getItemCount(ItemType.REVIVE_COIN);
        console.log(`当前复活币数量: ${reviveCoinCount}`);

        if (reviveCoinCount > 0) {
            console.log("复活币足够，开始复活流程");

            // 扣除一个复活币
            ItemManager.setItemCount(ItemType.REVIVE_COIN, reviveCoinCount - 1);
            console.log(`扣除1个复活币，剩余: ${reviveCoinCount - 1}`);

            // 增加复活次数计数
            GameData.useReviveCoin();

            // 保存当前游戏状态用于复活
            const currentScore = GameData.getScore();
            const currentSessionCoins = GameData.getSessionCoins();
            GameData.setReviveState(currentScore, currentSessionCoins);

            // 隐藏游戏结束界面，直接切换到准备状态进行复活
            console.log("复活：切换到游戏准备状态");
            this.hide();

            // 获取GameManager实例并切换到准备状态
            const gameManager = GameManager.inst();
            if (gameManager) {
                gameManager.transitionToReadyState();
            } else {
                console.error("无法获取GameManager实例");
            }
        } else {
            console.log("复活币不足，显示提示");
            this.showReviveLackSprite();
        }
    }

    /**
     * 显示复活币不足提示
     */
    private showReviveLackSprite(): void {
        if (this.reviveLackSprite) {
            this.reviveLackSprite.active = true;
            console.log("显示复活币不足提示");

            // 2秒后自动隐藏
            this.scheduleOnce(() => {
                if (this.reviveLackSprite && this.reviveLackSprite.isValid) {
                    this.reviveLackSprite.active = false;
                    console.log("隐藏复活币不足提示");
                }
            }, 2.0);
        } else {
            console.error("复活币不足提示节点未设置！");
        }
    }

    /**
     * 显示复活次数上限提示
     */
    private showReviveLimitSprite(): void {
        if (this.reviveLimitSprite) {
            this.reviveLimitSprite.active = true;
            console.log("显示复活次数上限提示");

            // 2秒后自动隐藏
            this.scheduleOnce(() => {
                if (this.reviveLimitSprite && this.reviveLimitSprite.isValid) {
                    this.reviveLimitSprite.active = false;
                    console.log("隐藏复活次数上限提示");
                }
            }, 2.0);
        } else {
            console.error("复活次数上限提示节点未设置！");
        }
    }

    // 点击返回主页按钮
    onHomeButtonClick(){
        // 重置分数
        GameData.resetScore();
        // 清除复活状态
        GameData.clearReviveState();
        // 重置本局复活次数
        GameData.resetSessionReviveCount();

        // 不再停止音乐，让主菜单场景自己处理BGM的播放
        // 注意：我们不再调用AudioMgr.inst.stop()

        // 加载Home场景
        director.loadScene('Home');
    }
}


